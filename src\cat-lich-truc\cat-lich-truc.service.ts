import { In } from 'typeorm';
import { database } from '../config';
import { LOG, MaLoaiHinhTruc } from '../constants';
import { insertLog } from '../logs/logs.service';
import { LichTruc, NgayTruc, Organization, User } from '../models';
import { CatLichTruc } from '../models/catLichTruc.model';
import { ERROR } from '../utils/error';
import { ICatLichTrucBanNoiVuBody } from './cat-lich-truc';

export const createCatLichTrucBanNoiVu = async (body: ICatLichTrucBanNoiVuBody, user: User) => {
  const repos = {
    donvi: database.getRepository(Organization),
    lichTruc: database.getRepository(LichTruc),
    ngayTruc: database.getRepository(NgayTruc),
    catLichTruc: database.getRepository(CatLichTruc),
  };

  const [lichTruc, ngayTruc, donViTruc<PERSON><PERSON><PERSON><PERSON>ong, donViTrucBanPho] = await Promise.all([
    repos.lichTruc.findOne({
      where: {
        maLoaiHinhTruc: MaLoaiHinhTruc.TBNV,
        thang: body.date.getMonth() + 1,
        nam: body.date.getFullYear(),
      },
    }),
    repos.ngayTruc.findOne({
      where: {
        ngay: body.date.getDate(),
        thang: body.date.getMonth() + 1,
        nam: body.date.getFullYear(),
      },
    }),
    repos.donvi.findOne({
      where: { code: body.trucBanTruongMaDonVi },
    }),
    repos.donvi.findOne({
      where: { code: body.trucBanPhoMaDonVi },
    })
  ])

  if (!lichTruc) {
    throw new Error(ERROR.LICH_TRUC_NOT_FOUND);
  }
  if (!ngayTruc) {
    throw new Error(ERROR.NGAY_TRUC_NOT_FOUND);
  }
  if (!donViTrucBanTruong) {
    throw new Error(ERROR.ORGANIZATION_NOT_FOUND);
  }
  if (!donViTrucBanPho) {
    throw new Error(ERROR.ORGANIZATION_NOT_FOUND);
  }

  const validateDuplicateCatLichTruc = await repos.catLichTruc.findOne({
    where: {
      idNgayTruc: ngayTruc.id,
      idLichTruc: lichTruc.id,
      maDonVi: In([donViTrucBanTruong.code, donViTrucBanPho.code]),
      maLoaiHinhTruc: MaLoaiHinhTruc.TBNV,
    },
  });

  if (validateDuplicateCatLichTruc) {
    throw new Error(ERROR.DATA_EXISTED);
  }

  const catLichTrucBanTruong = new CatLichTruc();
  catLichTrucBanTruong.idNgayTruc = ngayTruc.id;
  catLichTrucBanTruong.idLichTruc = lichTruc.id;
  catLichTrucBanTruong.maLoaiHinhTruc = MaLoaiHinhTruc.TBNV;
  catLichTrucBanTruong.maLoaiHinhTrucCon = MaLoaiHinhTruc.TBNV_TRUONG;
  catLichTrucBanTruong.stt = 1;
  catLichTrucBanTruong.maDonVi = donViTrucBanTruong.code;

  const catLichTrucBanPho = new CatLichTruc();
  catLichTrucBanPho.idNgayTruc = ngayTruc.id;
  catLichTrucBanPho.idLichTruc = lichTruc.id;
  catLichTrucBanPho.maLoaiHinhTruc = MaLoaiHinhTruc.TBNV;
  catLichTrucBanPho.maLoaiHinhTrucCon = MaLoaiHinhTruc.TBNV_PHO;
  catLichTrucBanPho.stt = 1;
  catLichTrucBanPho.maDonVi = donViTrucBanPho.code;

  const catLichTrucBan = await Promise.all([
    repos.catLichTruc.save(catLichTrucBanTruong),
    repos.catLichTruc.save(catLichTrucBanPho),
  ]);

  await insertLog({
    ...(user && {
      content: `Tạo mới cắt lịch trực ban nội vụ`,
    }),
    ip: '127.0.0.1',
    ...(user && { userId: user.id }),
    typeId: LOG.CREATE,
    createdAt: new Date(),
    updatedAt: new Date(),
  });
  return { data: catLichTrucBan };
};
